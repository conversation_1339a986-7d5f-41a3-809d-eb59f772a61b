{"name": "dai-agents-sdk", "version": "1.0.0", "description": "A SDK for building and managing decentralized Multi AI agents communications using smart contracts.", "main": "dist/index.js", "type": "module", "scripts": {"start": "tsc && node dist/index.js", "dev": "tsx watch src/index.ts", "build": "tsc", "test": "tsc && node dist/test.js"}, "keywords": ["blockchain", "ai", "agents", "ethereum", "smart-contracts"], "author": "", "license": "ISC", "packageManager": "pnpm@10.15.0", "dependencies": {"ethers": "^6.13.4", "eventemitter3": "^5.0.1"}, "devDependencies": {"@types/node": "^24.3.0", "ts-node": "^10.9.2", "tsx": "^4.20.5", "typescript": "^5.9.2"}}