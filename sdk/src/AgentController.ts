import {
  Contract,
  ContractFactory,
  JsonRpcProvider,
  Wallet,
  Interface,
  TransactionResponse,
  ContractTransactionResponse
} from 'ethers';
import {
  Agent,
  AgentRole,
  RegisterAgentParams,
  SendMessageParams,
  RespondToMessageParams,
  TransactionOptions,
  DeploymentConfig
} from './types.js';
import { loadContractArtifact } from './utils/contractLoader.js';

// Load contract artifact
const contractArtifact = loadContractArtifact();

/**
 * AgentController ABI - loaded from contract artifact
 */
export const AGENT_CONTROLLER_ABI = contractArtifact.abi;

/**
 * AgentController bytecode for deployment
 */
export const AGENT_CONTROLLER_BYTECODE = "0x6080604052348015600e575f5ffd5b506111628061001c5f395ff3fe608060405234801561000f575f5ffd5b5060043610610055575f3560e01c80630d34dbc414610059578063b4847de214610075578063b84597ee146100a5578063d6cd4a02146100c1578063dc67488d146100df575b5f5ffd5b610073600480360381019061006e9190610880565b6100fd565b005b61008f600480360381019061008a91906108fd565b6101db565b60405161009c9190610994565b60405180910390f35b6100bf60048036038101906100ba91906109ad565b6103df565b005b6100c96104a4565b6040516100d69190610c1d565b60405180910390f35b6100e76106bf565b6040516100f49190610994565b60405180910390f35b8160025411610141576040517f08c379a000000000000000000000000000000000000000000000000000000000815260040161013890610c97565b60405180910390fd5b5f60015f3373ffffffffffffffffffffffffffffffffffffffff1673ffffffffffffffffffffffffffffffffffffffff1681526020019081526020015f205490505f60035f81548092919061019590610ce2565b9190505590508382827f59ce13b6ce44021bdd9403c552b1b6381680a999fb84a2a6b32eaf6e0e909414866040516101cd9190610d61565b60405180910390a450505050565b5f5f60025f8154809291906101ef90610ce2565b919050559050604051806080016040528082815260200186815260200185600181111561021f5761021e610ab1565b5b8152602001848152505f5f8381526020019081526020015f205f820151815f015560208201518160010190816102559190610f7e565b506040820151816002015f6101000a81548160ff021916908360018111156102805761027f610ab1565b5b0217905550606082015181600301908161029a9190610f7e565b509050508060015f3373ffffffffffffffffffffffffffffffffffffffff1673ffffffffffffffffffffffffffffffffffffffff1681526020019081526020015f20819055505f5f60018111156102f4576102f3610ab1565b5b85600181111561030757610306610ab1565b5b14610347576040518060400160405280600481526020017f436861740000000000000000000000000000000000000000000000000000000081525061037e565b6040518060400160405280600581526020017f4167656e740000000000000000000000000000000000000000000000000000008152505b90503373ffffffffffffffffffffffffffffffffffffffff16827fcf07c596ae4da623a80266c34379db1a6f349ea922be113e509f83be547514658884886040516103cb9392919061104d565b60405180910390a381925050509392505050565b8160025411610423576040517f08c379a000000000000000000000000000000000000000000000000000000000815260040161041a906110e1565b60405180910390fd5b5f60015f3373ffffffffffffffffffffffffffffffffffffffff1673ffffffffffffffffffffffffffffffffffffffff1681526020019081526020015f205490508281857fe62fb610b5728a43fd93319dd0aeef3ae14cb5e5e4b6cb76626365df18e275a3856040516104969190610d61565b60405180910390a450505050565b60605f60025467ffffffffffffffff8111156104c3576104c261075c565b5b6040519080825280602002602001820160405280156104fc57816020015b6104e96106c8565b8152602001906001900390816104e15790505b5090505f5f90505b6002548110156106b7575f5f8281526020019081526020015f206040518060800160405290815f820154815260200160018201805461054290610dae565b80601f016020809104026020016040519081016040528092919081815260200182805461056e90610dae565b80156105b95780601f10610590576101008083540402835291602001916105b9565b820191905f5260205f20905b81548152906001019060200180831161059c57829003601f168201915b50505050508152602001600282015f9054906101000a900460ff1660018111156105e6576105e5610ab1565b5b60018111156105f8576105f7610ab1565b5b815260200160038201805461060c90610dae565b80601f016020809104026020016040519081016040528092919081815260200182805461063890610dae565b80156106835780601f1061065a57610100808354040283529160200191610683565b820191905f5260205f20905b81548152906001019060200180831161066657829003601f168201915b50505050508152505082828151811061069f5761069e6110ff565b5b60200260200101819052508080600101915050610504565b508091505090565b5f600254905090565b60405180608001604052805f8152602001606081526020015f60018111156106f3576106f2610ab1565b5b8152602001606081525090565b5f604051905090565b5f5ffd5b5f5ffd5b5f819050919050565b61072381610711565b811461072d575f5ffd5b50565b5f8135905061073e8161071a565b92915050565b5f5ffd5b5f5ffd5b5f601f19601f8301169050919050565b7f4e487b71000000000000000000000000000000000000000000000000000000005f52604160045260245ffd5b6107928261074c565b810181811067ffffffffffffffff821117156107b1576107b061075c565b5b80604052505050565b5f6107c3610700565b90506107cf8282610789565b919050565b5f67ffffffffffffffff8211156107ee576107ed61075c565b5b6107f78261074c565b9050602081019050919050565b828183375f83830152505050565b5f61082461081f846107d4565b6107ba565b9050828152602081018484840111156108405761083f610748565b5b61084b848285610804565b509392505050565b5f82601f83011261086757610866610744565b5b8135610877848260208601610812565b91505092915050565b5f5f6040838503121561089657610895610709565b5b5f6108a385828601610730565b925050602083013567ffffffffffffffff8111156108c4576108c361070d565b5b6108d085828601610853565b9150509250929050565b600281106108e6575f5ffd5b50565b5f813590506108f7816108da565b92915050565b5f5f5f6060848603121561091457610913610709565b5b5f84013567ffffffffffffffff8111156109315761093061070d565b5b61093d86828701610853565b935050602061094e868287016108e9565b925050604084013567ffffffffffffffff81111561096f5761096e61070d565b5b61097b86828701610853565b9150509250925092565b61098e81610711565b82525050565b5f6020820190506109a75f830184610985565b92915050565b5f5f5f606084860312156109c4576109c3610709565b5b5f6109d186828701610730565b93505060206109e286828701610730565b925050604084013567ffffffffffffffff811115610a0357610a0261070d565b5b610a0f86828701610853565b9150509250925092565b5f81519050919050565b5f82825260208201905092915050565b5f819050602082019050919050565b610a4b81610711565b82525050565b5f81519050919050565b5f82825260208201905092915050565b8281835e5f83830152505050565b5f610a8382610a51565b610a8d8185610a5b565b9350610a9d818560208601610a6b565b610aa68161074c565b840191505092915050565b7f4e487b71000000000000000000000000000000000000000000000000000000005f52602160045260245ffd5b60028110610aef57610aee610ab1565b5b50565b5f819050610aff82610ade565b919050565b5f610b0e82610af2565b9050919050565b610b1e81610b04565b82525050565b5f608083015f830151610b395f860182610a42565b5060208301518482036020860152610b518282610a79565b9150506040830151610b666040860182610b15565b5060608301518482036060860152610b7e8282610a79565b9150508091505092915050565b5f610b968383610b24565b905092915050565b5f602082019050919050565b5f610bb482610a19565b610bbe8185610a23565b935083602082028501610bd085610a33565b805f5b85811015610c0b5784840389528151610bec8582610b8b565b9450610bf783610b9e565b925060208a01995050600181019050610bd3565b50829750879550505050505092915050565b5f6020820190508181035f830152610c358184610baa565b905092915050565b5f82825260208201905092915050565b7f4147454e545f4e4f545f464f554e4400000000000000000000000000000000005f82015250565b5f610c81600f83610c3d565b9150610c8c82610c4d565b602082019050919050565b5f6020820190508181035f830152610cae81610c75565b9050919050565b7f4e487b71000000000000000000000000000000000000000000000000000000005f52601160045260245ffd5b5f610cec82610711565b91507fffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffff8203610d1e57610d1d610cb5565b5b600182019050919050565b5f610d3382610a51565b610d3d8185610c3d565b9350610d4d818560208601610a6b565b610d568161074c565b840191505092915050565b5f6020820190508181035f830152610d798184610d29565b905092915050565b7f4e487b71000000000000000000000000000000000000000000000000000000005f52602260045260245ffd5b5f6002820490506001821680610dc557607f821691505b602082108103610dd857610dd7610d81565b5b50919050565b5f819050815f5260205f209050919050565b5f6020601f8301049050919050565b5f82821b905092915050565b5f60088302610e3a7fffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffff82610dff565b610e448683610dff565b95508019841693508086168417925050509392505050565b5f819050919050565b5f610e7f610e7a610e7584610711565b610e5c565b610711565b9050919050565b5f819050919050565b610e9883610e65565b610eac610ea482610e86565b848454610e0b565b825550505050565b5f5f905090565b610ec3610eb4565b610ece818484610e8f565b505050565b5b81811015610ef157610ee65f82610ebb565b600181019050610ed4565b5050565b601f821115610f3657610f0781610dde565b610f1084610df0565b81016020851015610f1f578190505b610f33610f2b85610df0565b830182610ed3565b50505b505050565b5f82821c905092915050565b5f610f565f1984600802610f3b565b1980831691505092915050565b5f610f6e8383610f47565b9150826002028217905092915050565b610f8782610a51565b67ffffffffffffffff811115610fa057610f9f61075c565b5b610faa8254610dae565b610fb5828285610ef5565b5f60209050601f831160018114610fe6575f8415610fd4578287015190505b610fde8582610f63565b865550611045565b601f198416610ff486610dde565b5f5b8281101561101b57848901518255600182019150602085019450602081019050610ff6565b868310156110385784890151611034601f891682610f47565b8355505b6001600288020188555050505b505050505050565b5f6060820190508181035f8301526110658186610d29565b905081810360208301526110798185610d29565b9050818103604083015261108d8184610d29565b9050949350505050565b7f524543565f4147454e545f4e4f545f464f554e440000000000000000000000005f82015250565b5f6110cb601483610c3d565b91506110d682611097565b602082019050919050565b5f6020820190508181035f8301526110f8816110bf565b9050919050565b7f4e487b71000000000000000000000000000000000000000000000000000000005f52603260045260245ffdfea2646970667358221220b097676b90e40bc7f46744e86b7bde79256e86b2096667846245c6e779aa7a3b64736f6c634300081e0033";

/**
 * AgentController class for interacting with the smart contract
 */
export class AgentController {
  public contract: Contract;
  public provider: JsonRpcProvider;
  public wallet?: Wallet;

  constructor(
    contractAddress: string,
    provider: JsonRpcProvider,
    wallet?: Wallet
  ) {
    this.provider = provider;
    this.wallet = wallet;
    
    if (wallet) {
      this.contract = new Contract(contractAddress, AGENT_CONTROLLER_ABI, wallet);
    } else {
      this.contract = new Contract(contractAddress, AGENT_CONTROLLER_ABI, provider);
    }
  }

  /**
   * Deploy a new AgentController contract
   */
  static async deploy(config: DeploymentConfig): Promise<AgentController> {
    const provider = new JsonRpcProvider(config.rpcUrl);
    const wallet = new Wallet(config.privateKey, provider);
    
    const factory = new ContractFactory(AGENT_CONTROLLER_ABI, AGENT_CONTROLLER_BYTECODE, wallet);
    
    const deployOptions: any = {};
    if (config.gasLimit) deployOptions.gasLimit = config.gasLimit;
    if (config.gasPrice) deployOptions.gasPrice = config.gasPrice;
    
    const contract = await factory.deploy(deployOptions);
    await contract.waitForDeployment();
    
    const contractAddress = await contract.getAddress();
    return new AgentController(contractAddress, provider, wallet);
  }

  /**
   * Register a new agent
   */
  async registerAgent(params: RegisterAgentParams): Promise<ContractTransactionResponse> {
    if (!this.wallet) {
      throw new Error('Wallet is required for write operations');
    }

    const tx = await this.contract.registerAgent(
      params.name,
      params.role,
      params.ipfsHash,
      params.options || {}
    );
    
    return tx;
  }

  /**
   * Send a message to an agent
   */
  async sendMessageToAgent(params: SendMessageParams): Promise<ContractTransactionResponse> {
    if (!this.wallet) {
      throw new Error('Wallet is required for write operations');
    }

    const tx = await this.contract.sendMessageToAgent(
      params.agentId,
      params.message,
      params.options || {}
    );
    
    return tx;
  }

  /**
   * Respond to a message
   */
  async respondToAgent(params: RespondToMessageParams): Promise<ContractTransactionResponse> {
    if (!this.wallet) {
      throw new Error('Wallet is required for write operations');
    }

    const tx = await this.contract.respondToAgent(
      params.messageId,
      params.receiverAgentId,
      params.response,
      params.options || {}
    );
    
    return tx;
  }

  /**
   * Get the total number of agents
   */
  async countAgents(): Promise<bigint> {
    return await this.contract.countAgents();
  }

  /**
   * List all registered agents
   */
  async listAgents(): Promise<Agent[]> {
    const agents = await this.contract.listAgents();
    return agents.map((agent: any) => ({
      id: agent.id,
      name: agent.name,
      role: agent.role,
      ipfsHash: agent.ipfsHash
    }));
  }

  /**
   * Get contract address
   */
  async getAddress(): Promise<string> {
    return await this.contract.getAddress();
  }
}
