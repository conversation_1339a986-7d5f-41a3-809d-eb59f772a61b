import { Chat<PERSON><PERSON>A<PERSON> } from '@langchain/openai';
import { AgentExecutor, createOpenAIFunctionsAgent } from 'langchain/agents';
import { ChatPromptTemplate } from '@langchain/core/prompts';
import { AgentSDK, NETWORKS } from 'dai-agents-sdk';
import { createDAIAgentsToolkit } from '../index.js';

/**
 * Complete example of using DAI Agents SDK with LangChain
 * 
 * This example shows how to:
 * 1. Create LangChain agents that can interact with the blockchain
 * 2. Register AI agents on DuckChain
 * 3. Enable agent-to-agent communication
 * 4. Use LangChain tools for blockchain operations
 */

// Configuration
const CONFIG = {
  // OpenAI API key for LangChain
  openaiApiKey: process.env.OPENAI_API_KEY || 'your-openai-api-key',
  
  // DuckChain configuration
  duckchainPrivateKey: process.env.DUCKCHAIN_PRIVATE_KEY || '0x...',
  contractAddress: process.env.CONTRACT_ADDRESS || '', // Leave empty to deploy new
};

/**
 * Create a LangChain agent with blockchain capabilities
 */
async function createBlockchainAgent(agentSDK: AgentSDK, systemPrompt: string) {
  // Initialize OpenAI chat model
  const llm = new ChatOpenAI({
    openAIApiKey: CONFIG.openaiApiKey,
    modelName: 'gpt-4',
    temperature: 0.7,
  });

  // Create DAI Agents toolkit
  const toolkit = createDAIAgentsToolkit(agentSDK);
  const tools = toolkit.getTools();

  // Create prompt template
  const prompt = ChatPromptTemplate.fromMessages([
    ['system', systemPrompt],
    ['human', '{input}'],
    ['placeholder', '{agent_scratchpad}'],
  ]);

  // Create the agent
  const agent = await createOpenAIFunctionsAgent({
    llm,
    tools,
    prompt,
  });

  // Create agent executor
  const agentExecutor = new AgentExecutor({
    agent,
    tools,
    verbose: true,
  });

  return { agentExecutor, toolkit };
}

/**
 * Example 1: Basic LangChain + Blockchain Integration
 */
async function basicExample() {
  console.log('🤖 Basic LangChain + Blockchain Integration');
  console.log('=' .repeat(60));

  try {
    // Initialize SDK
    const sdk = new AgentSDK({
      rpcUrl: NETWORKS.duckchain.rpcUrl,
      privateKey: CONFIG.duckchainPrivateKey,
      contractAddress: CONFIG.contractAddress,
    });

    // Create LangChain agent with blockchain tools
    const { agentExecutor, toolkit } = await createBlockchainAgent(
      sdk, 
      'You are a helpful AI assistant that can communicate with other agents on the DuckChain blockchain. You can register agents, send messages, and coordinate with other AI agents.'
    );

    // Start event monitoring
    await toolkit.startEventMonitoring((message) => {
      console.log('📡 Blockchain Event:', message.content);
    });

    // Example interactions
    console.log('\n🔄 Agent Interactions:');
    
    // Register an agent
    const registerResult = await agentExecutor.invoke({
      input: 'Register a new agent called "LangChain Bot" with role "chat"'
    });
    console.log('✅ Register Result:', registerResult.output);

    // List all agents
    const listResult = await agentExecutor.invoke({
      input: 'Show me all registered agents on the blockchain'
    });
    console.log('📋 List Result:', listResult.output);

    // Send a message (if there are other agents)
    const agents = await sdk.getAllAgents();
    if (agents.length > 1) {
      const targetAgent = agents.find(a => a.name !== 'LangChain Bot');
      if (targetAgent) {
        const messageResult = await agentExecutor.invoke({
          input: `Send a message "Hello from LangChain!" to agent ID ${targetAgent.id}`
        });
        console.log('💬 Message Result:', messageResult.output);
      }
    }

    // Stop monitoring
    toolkit.stopEventMonitoring();
    
  } catch (error) {
    console.error('❌ Basic example failed:', error);
  }
}

/**
 * Example 2: Multi-Agent Conversation
 */
async function multiAgentExample() {
  console.log('\n🤖🤖 Multi-Agent Blockchain Conversation');
  console.log('=' .repeat(60));

  try {
    // Initialize SDK
    const sdk = new AgentSDK({
      rpcUrl: NETWORKS.duckchain.rpcUrl,
      privateKey: CONFIG.duckchainPrivateKey,
      contractAddress: CONFIG.contractAddress,
    });

    // Create two different LangChain agents
    const { agentExecutor: agent1 } = await createBlockchainAgent(
      sdk, 
      'You are a helpful assistant agent that can communicate with other agents on the blockchain.'
    );
    
    const { agentExecutor: agent2, toolkit } = await createBlockchainAgent(
      sdk, 
      'You are an analytics agent that processes data and provides insights. You can communicate with other agents on the blockchain to share analysis results.'
    );

    // Start event monitoring
    const messages: any[] = [];
    await toolkit.startEventMonitoring((message) => {
      messages.push(message);
      console.log('📡 New blockchain event:', message.content);
    });

    // Register both agents
    console.log('\n📝 Registering agents...');
    
    const agent1Result = await agent1.invoke({
      input: 'Register me as "Assistant Agent" with role "chat"'
    });
    console.log('Agent 1 registered:', agent1Result.output);

    await new Promise(resolve => setTimeout(resolve, 3000)); // Wait for blockchain

    const agent2Result = await agent2.invoke({
      input: 'Register me as "Analytics Agent" with role "agent"'
    });
    console.log('Agent 2 registered:', agent2Result.output);

    await new Promise(resolve => setTimeout(resolve, 3000)); // Wait for blockchain

    // Get agent IDs for communication
    const allAgents = await sdk.getAllAgents();
    const assistantAgent = allAgents.find(a => a.name.includes('Assistant'));
    const analyticsAgent = allAgents.find(a => a.name.includes('Analytics'));

    if (assistantAgent && analyticsAgent) {
      console.log('\n💬 Starting agent conversation...');
      
      // Agent 1 sends initial message
      const initialMessage = await agent1.invoke({
        input: `Send a message to agent ID ${analyticsAgent.id}: "Hello Analytics Agent! Can you help me analyze some data?"`
      });
      console.log('📤 Initial message sent:', initialMessage.output);

      await new Promise(resolve => setTimeout(resolve, 5000)); // Wait for event

      // Agent 2 responds
      const latestMessages = messages.filter(m => m.additional_kwargs?.messageId);
      if (latestMessages.length > 0) {
        const lastMessage = latestMessages[latestMessages.length - 1];
        const messageId = lastMessage.additional_kwargs.messageId;
        
        const response = await agent2.invoke({
          input: `Respond to message ID ${messageId} from agent ${assistantAgent.id} with: "Hello! I'd be happy to help with data analysis. What kind of data do you need analyzed?"`
        });
        console.log('📤 Response sent:', response.output);
      }
    }

    // Wait for final events
    await new Promise(resolve => setTimeout(resolve, 5000));
    
    console.log(`\n📊 Total blockchain events captured: ${messages.length}`);
    toolkit.stopEventMonitoring();
    
  } catch (error) {
    console.error('❌ Multi-agent example failed:', error);
  }
}

/**
 * Main function to run examples
 */
async function runExamples() {
  console.log('🦆🤖 DAI Agents SDK + LangChain Integration Examples');
  console.log('🚀 Decentralized AI Agent Communication with LangChain');
  console.log('=' .repeat(80));

  if (!CONFIG.openaiApiKey || CONFIG.openaiApiKey === 'your-openai-api-key') {
    console.log('⚠️  Please set OPENAI_API_KEY environment variable');
    return;
  }

  if (!CONFIG.duckchainPrivateKey || CONFIG.duckchainPrivateKey === '0x...') {
    console.log('⚠️  Please set DUCKCHAIN_PRIVATE_KEY environment variable');
    return;
  }

  try {
    // Run examples sequentially
    await basicExample();
    await multiAgentExample();
    
    console.log('\n🎉 All LangChain integration examples completed!');
    
  } catch (error) {
    console.error('❌ Examples failed:', error);
  }
}

// Run examples if this file is executed directly
if (import.meta.url === `file://${process.argv[1]}`) {
  runExamples().catch(console.error);
}

export { runExamples, basicExample, multiAgentExample };
