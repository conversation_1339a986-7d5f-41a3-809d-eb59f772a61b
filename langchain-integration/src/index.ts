import { BaseTool } from '@langchain/core/tools';
import { BaseMessage, HumanMessage, AIMessage } from '@langchain/core/messages';
import { CallbackManagerForToolRun } from '@langchain/core/callbacks/manager';
import { AgentSDK, AgentRole } from 'dai-agents-sdk';
import { z } from 'zod';

/**
 * LangChain integration for DAI Agents SDK
 * Provides tools for AI agents to communicate on the blockchain
 */

/**
 * <PERSON><PERSON> for registering a new agent on the blockchain
 */
export class RegisterAgentTool extends BaseTool {
  name = 'register_blockchain_agent';
  description = 'Register a new AI agent on the blockchain for decentralized communication';
  
  schema = z.object({
    name: z.string().describe('Name of the AI agent'),
    role: z.enum(['agent', 'chat']).describe('Role of the agent: agent (with tools) or chat (chat-only)'),
    ipfsHash: z.string().optional().describe('IPFS hash for agent metadata'),
  });

  constructor(private agentSDK: AgentSDK) {
    super();
  }

  async _call(
    input: z.infer<typeof this.schema>,
    runManager?: CallbackManagerForToolRun
  ): Promise<string> {
    try {
      runManager?.handleToolStart(this.name, input);
      
      const role = input.role === 'agent' ? AgentRole.Agent : AgentRole.Chat;
      
      const result = await this.agentSDK.createNewAgent({
        name: input.name,
        role,
        ipfsHash: input.ipfsHash || `QmAgent${Date.now()}`,
      });

      const response = `Successfully registered agent "${input.name}" with ID: ${result.agentId}. Transaction: ${result.transactionHash}`;
      
      runManager?.handleToolEnd(response);
      return response;
    } catch (error: any) {
      const errorMsg = `Failed to register agent: ${error.message}`;
      runManager?.handleToolError(error);
      throw new Error(errorMsg);
    }
  }
}

/**
 * Tool for sending messages to other agents on the blockchain
 */
export class SendMessageTool extends BaseTool {
  name = 'send_blockchain_message';
  description = 'Send a message to another AI agent on the blockchain';
  
  schema = z.object({
    agentId: z.string().describe('ID of the target agent to send message to'),
    message: z.string().describe('Message content to send'),
  });

  constructor(private agentSDK: AgentSDK) {
    super();
  }

  async _call(
    input: z.infer<typeof this.schema>,
    runManager?: CallbackManagerForToolRun
  ): Promise<string> {
    try {
      runManager?.handleToolStart(this.name, input);
      
      const result = await this.agentSDK.sendMessage({
        agentId: BigInt(input.agentId),
        message: input.message,
      });

      const response = `Message sent successfully! Message ID: ${result.messageId}, Transaction: ${result.transactionHash}`;
      
      runManager?.handleToolEnd(response);
      return response;
    } catch (error: any) {
      const errorMsg = `Failed to send message: ${error.message}`;
      runManager?.handleToolError(error);
      throw new Error(errorMsg);
    }
  }
}

/**
 * Tool for responding to messages from other agents
 */
export class RespondToMessageTool extends BaseTool {
  name = 'respond_to_blockchain_message';
  description = 'Respond to a message from another AI agent on the blockchain';
  
  schema = z.object({
    messageId: z.string().describe('ID of the message to respond to'),
    receiverAgentId: z.string().describe('ID of the agent that sent the original message'),
    response: z.string().describe('Response content'),
  });

  constructor(private agentSDK: AgentSDK) {
    super();
  }

  async _call(
    input: z.infer<typeof this.schema>,
    runManager?: CallbackManagerForToolRun
  ): Promise<string> {
    try {
      runManager?.handleToolStart(this.name, input);
      
      const result = await this.agentSDK.respondToMessage({
        messageId: BigInt(input.messageId),
        receiverAgentId: BigInt(input.receiverAgentId),
        response: input.response,
      });

      const response = `Response sent successfully! Transaction: ${result.transactionHash}`;
      
      runManager?.handleToolEnd(response);
      return response;
    } catch (error: any) {
      const errorMsg = `Failed to send response: ${error.message}`;
      runManager?.handleToolError(error);
      throw new Error(errorMsg);
    }
  }
}

/**
 * Tool for listing all registered agents
 */
export class ListAgentsTool extends BaseTool {
  name = 'list_blockchain_agents';
  description = 'Get a list of all registered AI agents on the blockchain';
  
  schema = z.object({});

  constructor(private agentSDK: AgentSDK) {
    super();
  }

  async _call(
    input: z.infer<typeof this.schema>,
    runManager?: CallbackManagerForToolRun
  ): Promise<string> {
    try {
      runManager?.handleToolStart(this.name, input);
      
      const agents = await this.agentSDK.getAllAgents();
      const agentCount = await this.agentSDK.getAgentCount();
      
      const agentList = agents.map((agent, index) => 
        `${index + 1}. ID: ${agent.id}, Name: ${agent.name}, Role: ${AgentRole[agent.role]}`
      ).join('\n');
      
      const response = `Found ${agentCount} registered agents:\n${agentList}`;
      
      runManager?.handleToolEnd(response);
      return response;
    } catch (error: any) {
      const errorMsg = `Failed to list agents: ${error.message}`;
      runManager?.handleToolError(error);
      throw new Error(errorMsg);
    }
  }
}

/**
 * LangChain toolkit for DAI Agents SDK
 * Provides all blockchain agent tools in one package
 */
export class DAIAgentsToolkit {
  private tools: BaseTool[];

  constructor(private agentSDK: AgentSDK) {
    this.tools = [
      new RegisterAgentTool(agentSDK),
      new SendMessageTool(agentSDK),
      new RespondToMessageTool(agentSDK),
      new ListAgentsTool(agentSDK),
    ];
  }

  /**
   * Get all available tools
   */
  getTools(): BaseTool[] {
    return this.tools;
  }

  /**
   * Get a specific tool by name
   */
  getTool(name: string): BaseTool | undefined {
    return this.tools.find(tool => tool.name === name);
  }

  /**
   * Start monitoring blockchain events and convert them to LangChain messages
   */
  async startEventMonitoring(
    onMessage?: (message: BaseMessage) => void,
    options?: { pollInterval?: number }
  ): Promise<void> {
    await this.agentSDK.startEventMonitoring({
      fromBlock: 'latest',
      pollInterval: options?.pollInterval || 5000,
    });

    // Convert blockchain events to LangChain messages
    this.agentSDK.onMessageSent((event) => {
      if (onMessage) {
        const message = new HumanMessage({
          content: `New message from Agent ${event.senderAgentId}: ${event.message}`,
          additional_kwargs: {
            messageId: event.messageId.toString(),
            senderAgentId: event.senderAgentId.toString(),
            receiverAgentId: event.receiverAgentId.toString(),
            blockNumber: event.blockNumber,
            transactionHash: event.transactionHash,
          },
        });
        onMessage(message);
      }
    });

    this.agentSDK.onMessageResponded((event) => {
      if (onMessage) {
        const message = new AIMessage({
          content: `Response from Agent ${event.senderAgentId}: ${event.response}`,
          additional_kwargs: {
            messageId: event.messageId.toString(),
            senderAgentId: event.senderAgentId.toString(),
            receiverAgentId: event.receiverAgentId.toString(),
            blockNumber: event.blockNumber,
            transactionHash: event.transactionHash,
          },
        });
        onMessage(message);
      }
    });

    this.agentSDK.onAgentRegistered((event) => {
      if (onMessage) {
        const message = new AIMessage({
          content: `New agent registered: ${event.name} (ID: ${event.agentId})`,
          additional_kwargs: {
            agentId: event.agentId.toString(),
            agentAddress: event.agentAddress,
            name: event.name,
            role: event.role,
            blockNumber: event.blockNumber,
            transactionHash: event.transactionHash,
          },
        });
        onMessage(message);
      }
    });
  }

  /**
   * Stop event monitoring
   */
  stopEventMonitoring(): void {
    this.agentSDK.stopEventMonitoring();
  }
}

/**
 * Helper function to create a DAI Agents toolkit for LangChain
 */
export function createDAIAgentsToolkit(agentSDK: AgentSDK): DAIAgentsToolkit {
  return new DAIAgentsToolkit(agentSDK);
}
