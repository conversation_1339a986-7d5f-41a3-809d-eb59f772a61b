{"name": "dai-agents-langchain", "version": "1.0.0", "description": "LangChain integration for DAI Agents SDK", "main": "dist/index.js", "type": "module", "scripts": {"build": "tsc", "dev": "tsx watch src/index.ts", "example": "tsc && node dist/examples/basic.js"}, "keywords": ["langchain", "blockchain", "ai-agents", "duckchain", "ethereum", "web3"], "author": "DAI Agents Team", "license": "MIT", "dependencies": {"dai-agents-sdk": "file:../sdk", "@langchain/core": "^0.1.0", "@langchain/openai": "^0.0.14", "langchain": "^0.1.0", "zod": "^3.22.0"}, "devDependencies": {"typescript": "^5.0.0", "tsx": "^4.0.0"}, "peerDependencies": {"@langchain/core": "^0.1.0", "@langchain/openai": "^0.0.14", "langchain": "^0.1.0"}}